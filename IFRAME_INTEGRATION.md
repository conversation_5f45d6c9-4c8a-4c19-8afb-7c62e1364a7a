# iframe 嵌入集成指南

本项目已支持嵌入到 iframe 中使用，提供了自动登录和界面定制功能。

## 功能特性

### 1. 自动登录支持

通过 URL 参数实现自动登录，无需用户手动输入账号密码。

**使用方法：**
```
http://your-domain.com?loginUserId=12345
```

**参数说明：**
- `loginUserId`: 用户ID，用于自动登录
- `tenantId`: 可选，租户ID，默认为 "000000"

**API 接口：**
- 接口地址：`/auth/ulogin`
- 请求方法：POST
- 请求参数：
  ```json
  {
    "grantType": "userid",
    "userId": "12345",
    "tenantId": "000000",
    "clientId": "your-client-id"
  }
  ```

### 2. 顶部导航栏隐藏控制

支持隐藏顶部导航栏以适应 iframe 嵌入场景。

**使用方法：**
```
http://your-domain.com?hiddenNavbar=Y
```

**参数说明：**
- `hiddenNavbar`: 设置为 "Y" 时隐藏顶部导航栏
- 注意：侧边栏不会被影响，保持正常显示

### 3. Logo 隐藏控制

支持隐藏所有页面中的 Logo 组件，包括导航栏、侧边栏、登录页面等位置的 Logo。

**使用方法：**
```
http://your-domain.com?hiddenLogo=Y
```

**参数说明：**
- `hiddenLogo`: 设置为 "Y" 时隐藏所有 AppLogo 组件
- 影响范围：导航栏 Logo、侧边栏 Logo、登录页面 Logo 等所有位置

### 4. 组合使用

可以同时使用多个参数：
```
http://your-domain.com?loginUserId=12345&hiddenNavbar=Y&hiddenLogo=Y&tenantId=2
```

## iframe 嵌入示例

### 基础嵌入

```html
<iframe
  src="http://your-domain.com?loginUserId=12345&hiddenNavbar=Y&hiddenLogo=Y"
  width="100%"
  height="800px"
  frameborder="0">
</iframe>
```

### 响应式嵌入

```html
<div style="position: relative; width: 100%; height: 600px;">
  <iframe
    src="http://your-domain.com?loginUserId=12345&hiddenNavbar=Y&hiddenLogo=Y"
    style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; border: none;">
  </iframe>
</div>
```

## 消息通信

### 监听 iframe 消息

```javascript
window.addEventListener('message', function(event) {
  if (event.origin !== 'http://your-domain.com') return;
  
  const { type, data } = event.data;
  
  switch (type) {
    case 'IFRAME_READY':
      console.log('iframe 已准备就绪', data);
      break;
    case 'USER_LOGIN':
      console.log('用户登录成功', data);
      break;
    case 'USER_LOGOUT':
      console.log('用户退出登录', data);
      break;
  }
});
```

### 向 iframe 发送消息

```javascript
const iframe = document.getElementById('your-iframe');

// 发送配置更新消息
iframe.contentWindow.postMessage({
  type: 'IFRAME_CONFIG_UPDATE',
  data: {
    theme: 'dark',
    language: 'en-US'
  }
}, 'http://your-domain.com');

// 发送主题变更消息
iframe.contentWindow.postMessage({
  type: 'IFRAME_THEME_CHANGE',
  data: {
    theme: 'dark'
  }
}, 'http://your-domain.com');
```

## 样式定制

iframe 模式下会自动应用特殊样式类 `iframe-mode`，可以通过 CSS 进行定制：

```css
/* 自定义 iframe 模式下的样式 */
.iframe-mode {
  /* 你的自定义样式 */
}

.iframe-mode .your-component {
  /* 针对特定组件的样式调整 */
}
```

## 安全考虑

1. **域名白名单**：建议在生产环境中配置允许嵌入的父域名白名单
2. **参数验证**：确保 `loginUserId` 参数的有效性和安全性
3. **HTTPS**：在生产环境中使用 HTTPS 协议
4. **CSP 策略**：配置适当的内容安全策略

## 测试

项目根目录下的 `iframe-test.html` 文件提供了完整的测试界面，可以：

1. 配置不同的 URL 参数
2. 测试自动登录功能
3. 测试导航栏隐藏功能
4. 测试消息通信功能

**使用方法：**
1. 启动项目：`npm run dev`
2. 在浏览器中打开 `iframe-test.html`
3. 配置参数并测试各项功能

## 故障排除

### 自动登录失败

1. 检查 `loginUserId` 参数是否正确
2. 检查用户是否存在且状态正常
3. 检查租户配置是否正确
4. 查看浏览器控制台错误信息

### 导航栏未隐藏

1. 确认 `hiddenNavbar=Y` 参数正确传递
2. 检查浏览器控制台是否有 JavaScript 错误
3. 确认样式文件正确加载

### Logo 未隐藏

1. 确认 `hiddenLogo=Y` 参数正确传递
2. 检查浏览器开发者工具，确认 `iframe-hidden-logo` CSS 类已添加到 body 元素
3. 检查 CSS 样式是否正确加载
4. 确认没有其他 CSS 规则覆盖了隐藏样式

### iframe 通信问题

1. 检查域名是否匹配
2. 确认消息格式正确
3. 检查浏览器的跨域策略

## 技术实现

### 文件结构

```
src/
├── api/auth/
│   ├── index.ts          # 添加了 uloginApi 接口
│   └── model.ts          # 扩展了登录参数类型
├── utils/
│   └── iframe.ts         # iframe 工具函数
├── logics/
│   └── initIframeConfig.ts # iframe 配置初始化
├── router/guard/
│   └── autoLoginGuard.ts # 自动登录守卫
├── design/
│   └── iframe.less       # iframe 模式样式
└── store/modules/
    └── user.ts           # 扩展了登录逻辑
```

### 关键组件

1. **自动登录守卫**：在路由守卫中检查 URL 参数并执行自动登录
2. **iframe 工具函数**：提供 URL 参数解析、消息通信等功能
3. **样式优化**：针对 iframe 环境的样式调整
4. **配置初始化**：在应用启动时初始化 iframe 相关配置
