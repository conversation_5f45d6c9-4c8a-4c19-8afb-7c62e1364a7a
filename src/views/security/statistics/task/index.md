**任务监控统计页面开发需求**

### 一、页面整体要求

* 页面顶部为 **通用筛选区域**：

  * **选择站点**：接口 `/business/station/optionSelect`，参数 `siteName`，用法参考系统其他页面。
  * **开始时间 & 结束时间**：参数 `startTime`、`endTime`，使用 `DateTimePicker`，支持快捷选项（近七天、近一个月、近三个月、近六个月、近一年等），默认「近七天」。
* 页面布局：卡片式布局，分区清晰。
* 各统计模块需独立封装为组件，提升扩展性与代码质量。

---

### 二、统计模块

1. **任务异常情况（统计卡片）**

   * 展示样式参考 `src/views/security/dashboard/glasses/GlassesStatsOverview.vue`。
   * 展示内容：异常总数、智慧检修异常数、智慧作业异常数、智慧料库异常数。
   * 接口：`/business/siteTaskStatisticsAnalysis/getTaskExceptionStatistics`。

2. **任务异常事件趋势（折线图）**

   * 维度：异常总数、智慧检修异常数、智慧作业异常数、智慧料库异常数。
   * 接口：`/business/siteTaskStatisticsAnalysis/getTaskStatisticsBySiteAndTime`。

3. **任务情况统计（柱状图）**

   * 展示内容：料库任务总数、作业管控任务总数、车辆检修任务总数。
   * 接口：`/business/siteTaskStatisticsAnalysis/getTaskStatisticsBySiteAndTime`。

4. **异常类型分布（环形饼图）**

   * 筛选条件：事件类型 `eventType`，字典 `event_task_type`。
   * 接口：`/business/siteTaskStatisticsAnalysis/getEventTypeDistribution`。

5. **模型调用情况（折柱混合图）**

   * 数据尚未有接口，请先 Mock 模拟。
   * 展示：

     * 模型-1：次数（柱形）、成功率（折线）
     * 模型-2：次数（柱形）、成功率（折线）
     * 模型-3：次数（柱形）、成功率（折线）
     * …

6. **站点任务趋势（折线图）**

   * 展示内容：任务总数。
   * 接口：`/business/siteTaskStatisticsAnalysis/getSiteTaskTrendChart`。

---

### 三、UI 设计规范

* **整体风格**：企业级数据可视化仪表盘，现代简洁风。
* **布局**：卡片式，白色背景，大圆角，柔和阴影。
* **交互**：渐变填充、悬浮提示。
* **配色**：以蓝色系为主，辅以青绿色、橙色和红色。
* **字体**：简洁无衬线，层级分明，强调专业、科技感和清晰易读。

---

### 四、开发规范

* 每个统计模块拆分为独立组件，便于维护与扩展。
* 接口参数及返回值详见 `api.md`。
