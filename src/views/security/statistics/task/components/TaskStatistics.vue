<template>
  <div class="task-statistics bg-white p-4 mx-4 mt-4 rounded-lg shadow-sm">
    <div class="mb-4">
      <h3 class="text-lg font-semibold text-gray-800">任务情况统计</h3>
    </div>

    <div ref="chartRef" class="w-full h-80"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onUnmounted, type Ref } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { getTaskStatisticsBySiteAndTime } from '@/api/security/siteTaskStatisticsAnalysis';
  import type { TaskStatisticsItem } from '@/api/security/siteTaskStatisticsAnalysis/model';

  defineOptions({ name: 'TaskStatistics' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const chartRef = ref<HTMLDivElement>();
  const loading = ref(false);
  const chartData = ref<TaskStatisticsItem[]>([]);

  const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 任务类型映射
  const taskTypeMap: Record<string, string> = {
    '1': '车辆检修任务',
    '2': '作业管控任务',
    '3': '料库任务',
  };

  // 获取图表数据
  async function fetchChartData(params?: any) {
    try {
      loading.value = true;
      const response = await getTaskStatisticsBySiteAndTime(params);
      chartData.value = response || [];
      updateChart();
    } catch (error) {
      console.error('获取任务情况统计数据失败:', error);
      chartData.value = [];
      updateChart();
    } finally {
      loading.value = false;
    }
  }

  // 更新图表
  function updateChart() {
    const categories = chartData.value.map(
      (item) => taskTypeMap[item.taskType] || `任务类型${item.taskType}`,
    );
    const taskCounts = chartData.value.map((item) => item.taskCount || 0);
    const completeCounts = chartData.value.map((item) => item.completeNum || 0);

    const option = {
      title: {
        text: '',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params: any) {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param: any) => {
            const dataIndex = param.dataIndex;
            const item = chartData.value[dataIndex];
            if (param.seriesName === '任务总数') {
              result += `${param.marker}${param.seriesName}: ${param.value}<br/>`;
            } else {
              result += `${param.marker}${param.seriesName}: ${param.value}<br/>`;
              result += `完成率: ${item?.completeRate || '0%'}<br/>`;
              result += `平均时长: ${item?.avgDurationHours || '0'}小时<br/>`;
            }
          });
          return result;
        },
      },
      legend: {
        data: ['任务总数', '完成数量'],
        top: 10,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: categories,
        axisLabel: {
          interval: 0,
          rotate: 0,
        },
      },
      yAxis: {
        type: 'value',
        name: '任务数量',
        nameTextStyle: {
          color: '#666',
        },
      },
      series: [
        {
          name: '任务总数',
          type: 'bar',
          data: taskCounts,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#1890ff' },
                { offset: 1, color: '#40a9ff' },
              ],
            },
          },
          barWidth: '30%',
        },
        {
          name: '完成数量',
          type: 'bar',
          data: completeCounts,
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: '#52c41a' },
                { offset: 1, color: '#73d13d' },
              ],
            },
          },
          barWidth: '30%',
        },
      ],
    };

    setOptions(option as any);
  }

  // 监听查询参数变化
  // watch(
  //   () => props.searchParams,
  //   (newParams) => {
  //     fetchChartData(newParams);
  //   },
  //   { deep: true, immediate: true },
  // );

  onMounted(() => {
    window.addEventListener('resize', resize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', resize);
  });

  // 暴露刷新方法
  defineExpose({
    refresh: (params?: any) => fetchChartData(params),
  });
</script>

<style scoped>
  .task-statistics {
    transition: all 0.3s ease;
  }
</style>
