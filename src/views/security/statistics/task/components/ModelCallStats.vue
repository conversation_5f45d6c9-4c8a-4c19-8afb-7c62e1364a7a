<template>
  <div class="model-call-stats bg-white p-4 mx-4 mt-4 rounded-lg shadow-sm">
    <div class="mb-4">
      <h3 class="text-lg font-semibold text-gray-800">模型调用情况</h3>
      <p class="text-sm text-gray-500 mt-1">（当前为模拟数据，待后端接口完善）</p>
    </div>

    <div ref="chartRef" class="w-full h-80"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onUnmounted, type Ref } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import dayjs from 'dayjs';

  defineOptions({ name: 'ModelCallStats' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const chartRef = ref<HTMLDivElement>();

  const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 生成Mock数据
  function generateMockData() {
    const models = ['智能识别模型', '异常检测模型', '预测分析模型'];
    const dates: string[] = [];
    const startDate = dayjs().subtract(6, 'day');

    for (let i = 0; i < 7; i++) {
      dates.push(startDate.add(i, 'day').format('MM-DD'));
    }

    const mockData = models.map((modelName) => {
      const callCounts = dates.map(() => Math.floor(Math.random() * 100) + 50);
      const successRates = dates.map(() => Math.floor(Math.random() * 20) + 80);

      return {
        modelName,
        callCounts,
        successRates,
      };
    });

    return { dates, mockData };
  }

  // 更新图表
  function updateChart() {
    const { dates, mockData } = generateMockData();

    const series: any[] = [];
    const colors = ['#1890ff', '#52c41a', '#faad14'];

    // 为每个模型创建柱状图（调用次数）和折线图（成功率）
    mockData.forEach((model, index) => {
      const color = colors[index % colors.length];

      // 调用次数柱状图
      series.push({
        name: `${model.modelName}-调用次数`,
        type: 'bar',
        yAxisIndex: 0,
        data: model.callCounts,
        itemStyle: {
          color: color,
        },
        barWidth: '15%',
        barGap: '10%',
        barCategoryGap: '20%',
      });

      // 成功率折线图
      series.push({
        name: `${model.modelName}-成功率`,
        type: 'line',
        yAxisIndex: 1,
        data: model.successRates,
        lineStyle: {
          color: color,
          width: 2,
        },
        itemStyle: {
          color: color,
        },
        symbol: 'circle',
        symbolSize: 6,
      });
    });

    const option = {
      title: {
        text: '',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          crossStyle: {
            color: '#999',
          },
        },
        formatter: function (params: any) {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param: any) => {
            if (param.seriesName.includes('调用次数')) {
              result += `${param.marker}${param.seriesName}: ${param.value}次<br/>`;
            } else if (param.seriesName.includes('成功率')) {
              result += `${param.marker}${param.seriesName}: ${param.value}%<br/>`;
            }
          });
          return result;
        },
      },
      legend: {
        data: series.map((s) => s.name),
        top: 10,
        type: 'scroll',
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '20%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisPointer: {
          type: 'shadow',
        },
      },
      yAxis: [
        {
          type: 'value',
          name: '调用次数',
          position: 'left',
          nameTextStyle: {
            color: '#666',
          },
          axisLabel: {
            formatter: '{value}次',
          },
        },
        {
          type: 'value',
          name: '成功率',
          position: 'right',
          nameTextStyle: {
            color: '#666',
          },
          axisLabel: {
            formatter: '{value}%',
          },
          min: 0,
          max: 100,
        },
      ],
      series: series,
    };

    setOptions(option as any);
  }

  // 监听查询参数变化（虽然是Mock数据，但保持接口一致性）
  // watch(
  //   () => props.searchParams,
  //   () => {
  //     updateChart();
  //   },
  //   { deep: true, immediate: true },
  // );

  onMounted(() => {
    window.addEventListener('resize', resize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', resize);
  });

  // 暴露刷新方法
  defineExpose({
    refresh: () => updateChart(),
  });
</script>

<style scoped>
  .model-call-stats {
    transition: all 0.3s ease;
  }
</style>
