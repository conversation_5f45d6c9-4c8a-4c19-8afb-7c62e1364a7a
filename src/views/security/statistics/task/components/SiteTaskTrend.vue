<template>
  <div class="site-task-trend bg-white p-4 mx-4 mt-4 rounded-lg shadow-sm">
    <div class="mb-4">
      <h3 class="text-lg font-semibold text-gray-800">站点任务趋势</h3>
    </div>

    <div ref="chartRef" class="w-full h-80"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onUnmounted, type Ref } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { getSiteTaskTrendChart } from '@/api/security/siteTaskStatisticsAnalysis';
  import type { SiteTaskTrendItem } from '@/api/security/siteTaskStatisticsAnalysis/model';

  defineOptions({ name: 'SiteTaskTrend' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const chartRef = ref<HTMLDivElement>();
  const loading = ref(false);
  const chartData = ref<SiteTaskTrendItem[]>([]);

  const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 获取图表数据
  async function fetchChartData(params?: any) {
    try {
      loading.value = true;
      const response = await getSiteTaskTrendChart(params);
      chartData.value = response || [];
      updateChart();
    } catch (error) {
      console.error('获取站点任务趋势数据失败:', error);
      chartData.value = [];
      updateChart();
    } finally {
      loading.value = false;
    }
  }

  // 更新图表
  function updateChart() {
    const dates = chartData.value.map((item) => item.xdata || '');
    const taskCounts = chartData.value.map((item) => item.totalCount || 0);

    const option = {
      title: {
        text: '',
        left: 'center',
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross',
          label: {
            backgroundColor: '#6a7985',
          },
        },
        formatter: function (params: any) {
          const param = params[0];
          return `${param.axisValue}<br/>
                  ${param.marker}任务总数: ${param.value}`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates,
        axisLabel: {
          rotate: 45,
          formatter: function (value: string) {
            // 格式化日期显示
            return value.split(' ')[0]; // 只显示日期部分
          },
        },
      },
      yAxis: {
        type: 'value',
        name: '任务数量',
        nameTextStyle: {
          color: '#666',
        },
        axisLabel: {
          formatter: '{value}',
        },
      },
      series: [
        {
          name: '任务总数',
          type: 'line',
          data: taskCounts,
          smooth: true,
          lineStyle: {
            color: '#1890ff',
            width: 3,
          },
          itemStyle: {
            color: '#1890ff',
            borderColor: '#fff',
            borderWidth: 2,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(24, 144, 255, 0.3)' },
                { offset: 1, color: 'rgba(24, 144, 255, 0.05)' },
              ],
            },
          },
          symbol: 'circle',
          symbolSize: 6,
          emphasis: {
            itemStyle: {
              color: '#1890ff',
              borderColor: '#fff',
              borderWidth: 3,
              shadowBlur: 10,
              shadowColor: 'rgba(24, 144, 255, 0.5)',
            },
            scale: true,
          },
        },
      ],
    };

    setOptions(option as any);
  }

  // 监听查询参数变化
  // watch(
  //   () => props.searchParams,
  //   (newParams) => {
  //     fetchChartData(newParams);
  //   },
  //   { deep: true, immediate: true },
  // );

  onMounted(() => {
    window.addEventListener('resize', resize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', resize);
  });

  // 暴露刷新方法
  defineExpose({
    refresh: (params?: any) => fetchChartData(params),
  });
</script>

<style scoped>
  .site-task-trend {
    transition: all 0.3s ease;
  }
</style>
