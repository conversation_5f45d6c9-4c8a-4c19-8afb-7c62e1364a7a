<template>
  <div class="task-exception-stats bg-white p-4 mx-4 mt-4 rounded-lg shadow-sm">
    <div class="mb-4">
      <h3 class="text-lg font-semibold text-gray-800">任务异常情况</h3>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <div
        v-for="(item, index) of statsModel"
        :key="index"
        class="stat-card bg-gradient-to-r rounded-lg p-4 flex items-center space-x-3 cursor-pointer"
        :class="item.bgClass"
      >
        <div class="icon-wrapper p-3 rounded-full bg-white bg-opacity-20">
          <!-- <Icon :icon="item.icon" class="text-2xl text-white" /> -->
        </div>

        <div class="flex-1 w-0">
          <div class="text-white text-sm opacity-90">{{ item.label }}</div>
          <div class="text-white text-2xl font-bold mt-1">
            {{ item.value || 0 }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, computed, watch } from 'vue';
  // import { Icon } from '@iconify/vue';
  import { getTaskExceptionStatistics } from '@/api/security/siteTaskStatisticsAnalysis';
  import type { TaskExceptionStats } from '@/api/security/siteTaskStatisticsAnalysis/model';

  defineOptions({ name: 'TaskExceptionStats' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const loading = ref(false);
  const statsData = ref<TaskExceptionStats>({
    totalExceptionCount: 0,
    vehicleExceptionCount: 0,
    controlExceptionCount: 0,
    warehouseExceptionCount: 0,
  });

  const statsModel = computed(() => {
    return [
      {
        label: '异常总数',
        value: statsData.value.totalExceptionCount,
        icon: 'mdi:alert-circle',
        bgClass: 'from-red-500 to-red-600',
      },
      {
        label: '智慧检修异常',
        value: statsData.value.vehicleExceptionCount,
        icon: 'mdi:car-wrench',
        bgClass: 'from-orange-500 to-orange-600',
      },
      {
        label: '智慧作业异常',
        value: statsData.value.controlExceptionCount,
        icon: 'mdi:cog-outline',
        bgClass: 'from-yellow-500 to-yellow-600',
      },
      {
        label: '智慧料库异常',
        value: statsData.value.warehouseExceptionCount,
        icon: 'mdi:warehouse',
        bgClass: 'from-purple-500 to-purple-600',
      },
    ];
  });

  // 获取统计数据
  async function fetchStatsData(params?: any) {
    try {
      loading.value = true;
      const response = await getTaskExceptionStatistics(params);
      statsData.value = response || {
        totalExceptionCount: 0,
        vehicleExceptionCount: 0,
        controlExceptionCount: 0,
        warehouseExceptionCount: 0,
      };
    } catch (error) {
      console.error('获取任务异常统计数据失败:', error);
      statsData.value = {
        totalExceptionCount: 0,
        vehicleExceptionCount: 0,
        controlExceptionCount: 0,
        warehouseExceptionCount: 0,
      };
    } finally {
      loading.value = false;
    }
  }

  // 监听查询参数变化
  // watch(
  //   () => props.searchParams,
  //   (newParams) => {
  //     fetchStatsData(newParams);
  //   },
  //   { deep: true, immediate: true },
  // );

  // 暴露刷新方法供父组件调用
  defineExpose({
    refresh: (params?: any) => fetchStatsData(params),
  });
</script>

<style scoped>
  .stat-card {
    transition: all 0.3s ease;
  }

  .stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 15%);
  }

  .icon-wrapper {
    transition: all 0.3s ease;
  }

  .stat-card:hover .icon-wrapper {
    transform: scale(1.1);
  }
</style>
