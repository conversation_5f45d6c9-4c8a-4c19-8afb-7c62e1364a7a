<template>
  <div class="event-type-distribution bg-white p-4 mx-4 mt-4 rounded-lg shadow-sm">
    <div class="mb-4 flex justify-between items-center">
      <h3 class="text-lg font-semibold text-gray-800">异常类型分布</h3>
      <a-select
        v-model:value="selectedEventType"
        placeholder="选择事件类型"
        style="width: 150px"
        allowClear
        @change="handleEventTypeChange"
      >
        <a-select-option value="">全部类型</a-select-option>
        <a-select-option
          v-for="option in eventTypeOptions"
          :key="option.value"
          :value="option.value"
        >
          {{ option.label }}
        </a-select-option>
      </a-select>
    </div>

    <div ref="chartRef" class="w-full h-80"></div>
  </div>
</template>

<script setup lang="ts">
  import { ref, watch, onMounted, onUnmounted, type Ref } from 'vue';
  import { useECharts } from '@/hooks/web/useECharts';
  import { getEventTypeDistribution } from '@/api/security/siteTaskStatisticsAnalysis';
  import type { EventTypeDistributionItem } from '@/api/security/siteTaskStatisticsAnalysis/model';

  defineOptions({ name: 'EventTypeDistribution' });

  interface Props {
    searchParams?: any;
  }

  const props = withDefaults(defineProps<Props>(), {
    searchParams: () => ({}),
  });

  const chartRef = ref<HTMLDivElement>();
  const loading = ref(false);
  const chartData = ref<EventTypeDistributionItem[]>([]);
  const selectedEventType = ref<string>('');

  const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 事件类型选项（根据字典 event_task_type）
  const eventTypeOptions = ref([
    { label: '设备故障', value: 'device_fault' },
    { label: '人员违规', value: 'personnel_violation' },
    { label: '环境异常', value: 'environment_abnormal' },
    { label: '流程异常', value: 'process_abnormal' },
    { label: '安全隐患', value: 'safety_hazard' },
  ]);

  // 颜色配置
  const colors = [
    '#1890ff',
    '#52c41a',
    '#faad14',
    '#f5222d',
    '#722ed1',
    '#13c2c2',
    '#eb2f96',
    '#fa8c16',
    '#a0d911',
    '#2f54eb',
  ];

  // 获取图表数据
  async function fetchChartData(params?: any) {
    try {
      loading.value = true;
      const queryParams = {
        ...params,
        eventType: selectedEventType.value || undefined,
      };
      const response = await getEventTypeDistribution(queryParams);
      chartData.value = response || [];
      updateChart();
    } catch (error) {
      console.error('获取异常类型分布数据失败:', error);
      chartData.value = [];
      updateChart();
    } finally {
      loading.value = false;
    }
  }

  // 更新图表
  function updateChart() {
    const data = chartData.value.map((item, index) => ({
      name: item.eventTypeName || '未知类型',
      value: item.eventCount || 0,
      itemStyle: {
        color: colors[index % colors.length],
      },
    }));

    const option = {
      title: {
        text: '',
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
        formatter: function (params: any) {
          const item = chartData.value.find((d) => d.eventTypeName === params.name);
          return `${params.name}<br/>
                  ${params.marker}数量: ${params.value}<br/>
                  占比: ${item?.percentage || 0}%`;
        },
      },
      legend: {
        type: 'scroll',
        orient: 'horizontal',
        bottom: 10,
        data: data.map((item) => item.name),
      },
      series: [
        {
          name: '异常类型分布',
          type: 'pie',
          radius: ['40%', '70%'],
          center: ['50%', '45%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderRadius: 8,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: 'bold',
              formatter: function (params: any) {
                return `${params.name}\n${params.value}`;
              },
            },
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
          labelLine: {
            show: false,
          },
          data: data,
        },
      ],
    };

    setOptions(option as any);
  }

  // 处理事件类型变化
  function handleEventTypeChange() {
    fetchChartData(props.searchParams);
  }

  // 监听查询参数变化
  // watch(
  //   () => props.searchParams,
  //   (newParams) => {
  //     fetchChartData(newParams);
  //   },
  //   { deep: true, immediate: true },
  // );

  onMounted(() => {
    window.addEventListener('resize', resize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', resize);
  });

  // 暴露刷新方法
  defineExpose({
    refresh: (params?: any) => fetchChartData(params),
  });
</script>

<style scoped>
  .event-type-distribution {
    transition: all 0.3s ease;
  }
</style>
