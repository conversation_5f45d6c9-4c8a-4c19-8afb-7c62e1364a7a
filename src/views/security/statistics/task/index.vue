<template>
  <div class="task-statistics-page min-h-screen bg-gray-50">
    <!-- 筛选表单 -->
    <FilterForm :loading="loading" @search="handleSearch" />

    <!-- 统计卡片 -->
    <TaskExceptionStats ref="taskExceptionStatsRef" :search-params="searchParams" />

    <!-- 图表区域 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-0">
      <!-- 任务异常事件趋势 -->
      <TaskExceptionTrend ref="taskExceptionTrendRef" :search-params="searchParams" />

      <!-- 任务情况统计 -->
      <TaskStatistics ref="taskStatisticsRef" :search-params="searchParams" />

      <!-- 异常类型分布 -->
      <EventTypeDistribution ref="eventTypeDistributionRef" :search-params="searchParams" />

      <!-- 模型调用情况 -->
      <ModelCallStats ref="modelCallStatsRef" :search-params="searchParams" />
    </div>

    <!-- 站点任务趋势 -->
    <SiteTaskTrend ref="siteTaskTrendRef" :search-params="searchParams" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive } from 'vue';
  import FilterForm from './components/FilterForm.vue';
  import TaskExceptionStats from './components/TaskExceptionStats.vue';
  import TaskExceptionTrend from './components/TaskExceptionTrend.vue';
  import TaskStatistics from './components/TaskStatistics.vue';
  import EventTypeDistribution from './components/EventTypeDistribution.vue';
  import ModelCallStats from './components/ModelCallStats.vue';
  import SiteTaskTrend from './components/SiteTaskTrend.vue';

  defineOptions({ name: 'TaskStatisticsPage' });

  const loading = ref(false);
  const searchParams = reactive({
    siteName: undefined as string | undefined,
    startTime: '',
    endTime: '',
  });

  // 组件引用
  const taskExceptionStatsRef = ref();
  const taskExceptionTrendRef = ref();
  const taskStatisticsRef = ref();
  const eventTypeDistributionRef = ref();
  const modelCallStatsRef = ref();
  const siteTaskTrendRef = ref();

  // 处理搜索
  function handleSearch(params: any) {
    Object.assign(searchParams, params);
    refreshAllComponents();
  }

  // 刷新所有组件
  function refreshAllComponents() {
    taskExceptionStatsRef.value?.refresh(searchParams);
    taskExceptionTrendRef.value?.refresh(searchParams);
    taskStatisticsRef.value?.refresh(searchParams);
    eventTypeDistributionRef.value?.refresh(searchParams);
    modelCallStatsRef.value?.refresh(searchParams);
    siteTaskTrendRef.value?.refresh(searchParams);
  }

  // 暴露刷新方法
  defineExpose({
    refresh: refreshAllComponents,
  });
</script>

<style scoped>
  .task-statistics-page {
    min-height: 100vh;
  }
</style>
