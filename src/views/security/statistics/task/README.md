# 任务监控统计页面

## 功能概述

任务监控统计页面是一个企业级数据可视化仪表盘，用于展示任务异常情况、趋势分析和统计数据。页面采用现代简洁的设计风格，提供丰富的交互功能和数据可视化图表。

## 页面结构

### 1. 筛选区域
- **站点选择**：支持选择不同站点进行数据筛选
- **时间范围**：支持自定义时间范围，提供快捷选项（近7天、近1个月、近3个月、近6个月、近1年）
- **默认设置**：页面加载时默认显示近7天的数据

### 2. 统计模块

#### 2.1 任务异常情况（统计卡片）
- 展示异常总数、智慧检修异常数、智慧作业异常数、智慧料库异常数
- 采用渐变色卡片设计，支持悬浮效果
- 使用图标增强视觉效果

#### 2.2 任务异常事件趋势（折线图）
- 展示不同类型异常事件的时间趋势
- 支持多条折线对比显示
- 提供详细的悬浮提示信息

#### 2.3 任务情况统计（柱状图）
- 展示料库任务、作业管控任务、车辆检修任务的统计信息
- 包含任务总数和完成数量的对比
- 悬浮提示显示完成率和平均时长

#### 2.4 异常类型分布（环形饼图）
- 展示不同异常类型的分布情况
- 支持事件类型筛选
- 提供百分比和数量信息

#### 2.5 模型调用情况（折柱混合图）
- 展示模型调用次数和成功率
- 使用Mock数据模拟（待后端接口完善）
- 支持多个模型的对比显示

#### 2.6 站点任务趋势（折线图）
- 展示站点任务总数的时间趋势
- 采用面积填充增强视觉效果
- 支持数据点高亮显示

## 技术实现

### 技术栈
- **前端框架**：Vue 3 + TypeScript
- **UI组件库**：Ant Design Vue 4.2.3
- **图表库**：ECharts 5.5.1
- **样式框架**：Tailwind CSS + UnoCSS
- **状态管理**：Pinia
- **HTTP客户端**：Axios

### 组件架构
```
src/views/security/statistics/task/
├── index.vue                          # 主页面
├── components/
│   ├── FilterForm.vue                 # 筛选表单组件
│   ├── TaskExceptionStats.vue         # 任务异常情况统计卡片
│   ├── TaskExceptionTrend.vue         # 任务异常事件趋势图
│   ├── TaskStatistics.vue             # 任务情况统计图
│   ├── EventTypeDistribution.vue      # 异常类型分布图
│   ├── ModelCallStats.vue             # 模型调用情况图
│   └── SiteTaskTrend.vue              # 站点任务趋势图
├── api.md                             # API接口文档
├── index.md                           # 需求文档
└── README.md                          # 说明文档
```

### API接口
```
src/api/security/siteTaskStatisticsAnalysis/
├── index.ts                           # API接口定义
└── model.ts                           # 数据模型定义
```

## 接口说明

### 已实现的接口
1. `getTaskExceptionStatistics` - 获取任务异常情况统计
2. `getTaskStatisticsBySiteAndTime` - 获取任务情况统计
3. `getEventTrend` - 获取异常事件趋势
4. `getEventTypeDistribution` - 获取异常类型分布
5. `getSiteTaskTrendChart` - 获取站点任务趋势图

### 接口参数
- `siteName`: 站点名称（可选）
- `startTime`: 开始时间
- `endTime`: 结束时间
- `eventType`: 事件类型（可选）

## 设计特点

### UI设计
- **整体风格**：企业级数据可视化仪表盘，现代简洁风格
- **布局**：卡片式布局，白色背景，大圆角，柔和阴影
- **配色**：以蓝色系为主，辅以青绿色、橙色和红色
- **交互**：渐变填充、悬浮提示、平滑动画效果

### 响应式设计
- 支持不同屏幕尺寸的自适应布局
- 图表自动调整大小
- 移动端友好的交互体验

## 使用说明

1. **页面访问**：导航到任务监控统计页面
2. **数据筛选**：使用顶部筛选表单选择站点和时间范围
3. **数据查看**：查看各个统计模块的数据和图表
4. **交互操作**：
   - 悬停查看详细信息
   - 点击图例控制数据显示
   - 使用图表缩放和平移功能

## 开发规范

### 组件设计原则
- 每个统计模块独立封装为组件
- 支持参数传递和事件通信
- 提供刷新方法供外部调用
- 统一的错误处理和加载状态

### 代码规范
- 使用TypeScript进行类型检查
- 遵循Vue 3 Composition API规范
- 统一的命名规范和代码格式
- 完善的注释和文档

## 后续优化

1. **性能优化**：图表懒加载、数据缓存
2. **功能扩展**：数据导出、报表生成
3. **用户体验**：更多交互功能、个性化设置
4. **数据处理**：实时数据更新、数据预处理

## 注意事项

- 模型调用情况模块当前使用Mock数据，待后端接口完善后替换
- 图表配置可根据实际需求进行调整
- 建议在生产环境中添加数据缓存机制
- 需要确保后端接口返回的数据格式与前端期望一致
