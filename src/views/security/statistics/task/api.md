---
title: 安晨
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 安晨

Base URLs:

# Authentication

# 国铁PC/任务监控

## GET 站点任务趋势图

GET /admin/business/siteTaskStatisticsAnalysis/getSiteTaskTrendChart

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|days|query|string| 否 |none|
|startTime|query|string| 否 |none|
|endTime|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "操作成功",
  "token": null,
  "uri": null,
  "data": [
    {
      "totalCount": 0,
      "xdata": "2025-08-01"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-02"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-03"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-04"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-05"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-06"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-07"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-08"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-09"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-10"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-11"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-12"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-13"
    },
    {
      "totalCount": 0,
      "xdata": "2025-08-14"
    }
  ],
  "request_time": null,
  "response_time": null,
  "cost_time": null,
  "debug_image_url": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» token|null|true|none||none|
|» uri|null|true|none||none|
|» data|[object]|true|none||none|
|»» totalCount|integer|true|none||none|
|»» xdata|string|true|none||none|
|» request_time|null|true|none||none|
|» response_time|null|true|none||none|
|» cost_time|null|true|none||none|
|» debug_image_url|null|true|none||none|

## GET 异常类型分布

GET /admin/business/siteTaskStatisticsAnalysis/getEventTypeDistribution

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|eventType|query|string| 否 |none|
|startTime|query|string| 否 |none|
|endTime|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 异常事件趋势

GET /admin/business/siteTaskStatisticsAnalysis/getEventTrend

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|startTime|query|string| 否 |none|
|endTime|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "操作成功",
  "token": null,
  "uri": null,
  "data": [
    {
      "dateStr": null,
      "totalEventCount": 0,
      "vehicleEventCount": 0,
      "controlEventCount": 0,
      "warehouseEventCount": 0
    }
  ],
  "request_time": null,
  "response_time": null,
  "cost_time": null,
  "debug_image_url": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» token|null|true|none||none|
|» uri|null|true|none||none|
|» data|[object]|true|none||none|
|»» dateStr|null|false|none||none|
|»» totalEventCount|integer|false|none||none|
|»» vehicleEventCount|integer|false|none||none|
|»» controlEventCount|integer|false|none||none|
|»» warehouseEventCount|integer|false|none||none|
|» request_time|null|true|none||none|
|» response_time|null|true|none||none|
|» cost_time|null|true|none||none|
|» debug_image_url|null|true|none||none|

## GET 任务异常情况统计

GET /admin/business/siteTaskStatisticsAnalysis/getTaskExceptionStatistics

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|startTime|query|string| 否 |none|
|endTime|query|string| 否 |none|
|siteName|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

## GET 任务情况统计

GET /admin/business/siteTaskStatisticsAnalysis/getTaskStatisticsBySiteAndTime

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|startTime|query|string| 否 |none|
|endTime|query|string| 否 |none|
|siteName|query|string| 否 |none|

> 返回示例

> 200 Response

```json
{
  "code": 200,
  "msg": "操作成功",
  "token": null,
  "uri": null,
  "data": [
    {
      "taskCount": 0,
      "completeNum": 0,
      "completeRate": "0%",
      "avgDurationHours": "0",
      "taskType": "1"
    },
    {
      "taskCount": 0,
      "completeNum": 0,
      "completeRate": "0%",
      "avgDurationHours": "0",
      "taskType": "2"
    },
    {
      "taskCount": 0,
      "completeNum": 0,
      "completeRate": "0",
      "avgDurationHours": "0",
      "taskType": "3"
    }
  ],
  "request_time": null,
  "response_time": null,
  "cost_time": null,
  "debug_image_url": null
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|none|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none||none|
|» msg|string|true|none||none|
|» token|null|true|none||none|
|» uri|null|true|none||none|
|» data|[object]|true|none||none|
|»» taskCount|integer|true|none||none|
|»» completeNum|integer|true|none||none|
|»» completeRate|string|true|none||none|
|»» avgDurationHours|string|true|none||none|
|»» taskType|string|true|none||none|
|» request_time|null|true|none||none|
|» response_time|null|true|none||none|
|» cost_time|null|true|none||none|
|» debug_image_url|null|true|none||none|

# 数据模型

