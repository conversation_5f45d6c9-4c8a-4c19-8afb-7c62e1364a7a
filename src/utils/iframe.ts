/**
 * iframe相关工具函数
 */

/**
 * 获取URL参数
 * @param name 参数名
 * @param url 可选，默认为当前页面URL
 * @returns 参数值或null
 */
export function getUrlParam(name: string, url?: string): string | null {
  const targetUrl = url || window.location.href;
  const urlObj = new URL(targetUrl);
  return urlObj.searchParams.get(name);
}

/**
 * 获取所有URL参数
 * @param url 可选，默认为当前页面URL
 * @returns 参数对象
 */
export function getAllUrlParams(url?: string): Record<string, string> {
  const targetUrl = url || window.location.href;
  const urlObj = new URL(targetUrl);
  const params: Record<string, string> = {};
  
  urlObj.searchParams.forEach((value, key) => {
    params[key] = value;
  });
  
  return params;
}

/**
 * 检查是否在iframe中
 * @returns 是否在iframe中
 */
export function isInIframe(): boolean {
  try {
    return window.self !== window.top;
  } catch (e) {
    // 如果跨域，访问window.top会抛出异常
    return true;
  }
}

/**
 * 获取iframe配置参数
 * @returns iframe配置对象
 */
export interface IframeConfig {
  loginUserId?: string;
  hiddenNavbar?: boolean;
  hiddenLogo?: boolean;
  isInIframe: boolean;
}

export function getIframeConfig(): IframeConfig {
  const loginUserId = getUrlParam('loginUserId');
  const hiddenNavbar = getUrlParam('hiddenNavbar');
  const hiddenLogo = getUrlParam('hiddenLogo');

  return {
    loginUserId: loginUserId || undefined,
    hiddenNavbar: hiddenNavbar === 'Y',
    hiddenLogo: hiddenLogo === 'Y',
    isInIframe: isInIframe(),
  };
}

/**
 * 向父窗口发送消息
 * @param message 消息内容
 * @param targetOrigin 目标域，默认为*
 */
export function postMessageToParent(message: any, targetOrigin: string = '*') {
  if (isInIframe() && window.parent) {
    window.parent.postMessage(message, targetOrigin);
  }
}

/**
 * 监听来自父窗口的消息
 * @param callback 回调函数
 * @returns 取消监听的函数
 */
export function listenToParentMessage(callback: (event: MessageEvent) => void): () => void {
  const handler = (event: MessageEvent) => {
    // 这里可以添加来源验证逻辑
    callback(event);
  };
  
  window.addEventListener('message', handler);
  
  return () => {
    window.removeEventListener('message', handler);
  };
}
